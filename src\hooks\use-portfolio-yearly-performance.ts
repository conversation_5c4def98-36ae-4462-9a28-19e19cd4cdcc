"use client";

import { useState, useEffect, useCallback } from "react";
import { SupportedCurrency } from "@/components/dashboard/currency-selector";

export interface YearlyPerformanceData {
  year: number;
  portfolioValue: number;
  yearOverYearChange: number;
  yearOverYearPercentage: number;
  hasData: boolean;
}

interface UsePortfolioYearlyPerformanceResult {
  data: YearlyPerformanceData[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export function usePortfolioYearlyPerformance(
  selectedPortfolios: string[],
  displayCurrency: SupportedCurrency
): UsePortfolioYearlyPerformanceResult {
  const [data, setData] = useState<YearlyPerformanceData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (selectedPortfolios.length === 0) {
      setData([]);
      setIsLoading(false);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Use GET for smaller lists, POST for larger ones - more than 4 portfolios
      const usePost = selectedPortfolios.length > 4;

      let response: Response;

      if (usePost) {
        response = await fetch("/api/dashboard/portfolio-yearly-performance", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            portfolioIds: selectedPortfolios,
            displayCurrency,
          }),
        });
      } else {
        const params = new URLSearchParams({
          portfolioIds: selectedPortfolios.join(","),
          displayCurrency,
        });
        response = await fetch(`/api/dashboard/portfolio-yearly-performance?${params}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error ||
            "Nu s-au putut încărca datele de performanță anuală ale portofoliului"
        );
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(
          result.error ||
            "Nu s-au putut încărca datele de performanță anuală ale portofoliului"
        );
      }
    } catch (err) {
      console.error("Error fetching portfolio yearly performance:", err);
      setError(
        err instanceof Error ? err.message : "Eroare la încărcarea datelor"
      );
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, [selectedPortfolios, displayCurrency]);

  useEffect(() => {
    fetchData();
  }, [selectedPortfolios, displayCurrency, fetchData]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
  };
}
